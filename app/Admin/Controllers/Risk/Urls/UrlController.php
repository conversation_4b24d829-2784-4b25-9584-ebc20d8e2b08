<?php

namespace App\Admin\Controllers\Risk\Urls;

use App\Admin\Actions\Grid\Risk\Url\Config;
use App\Admin\Actions\Grid\Risk\Url\DelUrl;
use App\Admin\Actions\Grid\Risk\Url\DownUrl;
use App\Admin\Actions\Grid\Risk\Url\RemarkUpdateUrl;
use App\Admin\Actions\Grid\Risk\Url\UpUrl;
use App\Admin\Repositories\DirectoryDictionary;
use App\Admin\Repositories\MerchantUrl;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl as MerchantUrlModel;
use App\Models\RiskCase;
use App\Models\RiskMcc;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Form\Row;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Widgets\Tooltip;

class UrlController extends AdminController
{
    protected $title = '网址管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(MerchantUrlModel::with(['brand', 'mcc', 'case'])->where('url_status', '<>', MerchantUrlModel::STATUS_DELETE)->orderByDesc('updated_at'), function (Grid $grid) {
            //            $grid->column('id')->sortable();

            $grid->fixColumns(1, -1);
            $grid->column('merchant_id');
            $grid->column('merchant_name');
            $grid->column('business_id');
            $grid->column('url_name');
            $grid->column('main_url_name');
            $grid->column('website_mode')->display(function ($val) {
                return MerchantUrlModel::$merchantWebsiteModeMap[$val] ?? '未知';
            });
            $grid->column('saas_url_name');
            $grid->column('mcc.name', 'MCC');
            $grid->column('brand.name', '网址品牌');
            $grid->column('url_status')->display(function ($val) {
                $tip = '';

                if ($val == MerchantUrlModel::STATUS_DOWN || $val == MerchantUrlModel::STATUS_REJECT) {
                    Tooltip::make('#' . $this->id)->title($this->status_remarks);
                    $tip = "<i id='{$this->id}' class='feather icon-help-circle'></i>";
                }

                return $tip . (MerchantUrlModel::$merchantStatusMap[$val] ?? '未知');
            });
            $grid->column('remarks')->display(function ($val) {
                return Modal::make()
                            ->lg()
                            ->title('修改备注')
                            ->body(RemarkUpdateUrl::make()->payload([
                                                                        'id'      => $this->id,
                                                                        'remarks' => $val,
                                                                    ]))
                            ->button("<a style='text-decoration: underline;'>$val</a>");
            });
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->disableQuickEdit();
                $actions->disableView();

                $statusActionsMap = [
                    MerchantUrlModel::STATUS_UP => [DownUrl::class],
                    MerchantUrlModel::STATUS_DOWN => [UpUrl::class, DelUrl::class],
                    MerchantUrlModel::STATUS_UNDER_REVIEW => [],
                    MerchantUrlModel::STATUS_REJECT => [DelUrl::class],
                ];

                // 是否显示 Config 按钮
                $showConfig = isset($actions->row->case) && $actions->row->case->case_status == RiskCase::CASE_STATUS_COMPLETE;

                if ($showConfig && $this->url_status != MerchantUrlModel::STATUS_REJECT) {
                    $actions->append(new Config(admin_trans_field('config')));
                }

                // 添加动态操作按钮
                if (isset($statusActionsMap[$this->url_status])) {
                    foreach ($statusActionsMap[$this->url_status] as $actionClass) {
                        $actions->append(new $actionClass($this->id, $this->url_name));
                    }
                }
            });

            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('merchant_id')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                            ->pluck('merchant_name', 'merchant_id')
                            ->map(static function ($item, $key) {
                                return $item . ':' . $key;
                            })
                            ->toArray()
                )->load('business_id', '/businesses/get_businesses');
                $filter->equal('business_id')->select(
                    MerchantBusiness::pluck('business_id', 'business_id')->toArray()
                );
                $filter->equal('url_name')->select(MerchantUrlModel::get()->pluck('url_name', 'url_name')->toArray());
                $filter->equal('website_mode')->select(MerchantUrlModel::$merchantWebsiteModeMap);

                $dictionary = new DirectoryDictionary();
                $mccList    = $dictionary->getTypeList('MCC');
                $filter->equal('d_mcc_id')->select($mccList);

                $filter->equal('url_status')->select(MerchantUrlModel::$merchantStatusMap);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new MerchantUrl(['merchant:merchant_id,source',
                                           'factors' => function ($query) {
                                               return $query->where('risk_case_id', request()->input('risk_case_id'));
                                           },
                                           'disclosures' => function ($query) {
                                               return $query->where('risk_case_id', request()->input('risk_case_id'));
                                           },
                                           'urlCaseInfo' => function ($query) {
                                               return $query->where('risk_case_id', request()->input('risk_case_id'));
                                           }
                                           ]), function (Form $form) {
            // cases 信息
            $form->tab(admin_trans_label('case'), function (Form $form) {
                if (request()->input('risk_case_id')) {
                    $case = RiskCase::with('riskCasesBid')
                                    ->where('id', request()->input('risk_case_id'))
                                    ->first();
                    $form->model()->setAttribute('case', $case);
                }
                $form->row(function (Row $row) {
                    $row->width(3)->display('case.case_number');
                    $row->width(3)->display('case.merchant_id');
                    $row->width(3)->display('case.riskCasesBid')->customFormat(function ($val) {
                       if (!isset($val)) {
                           return '';
                       }

                       return $val[0]['business_id'] ?? '';
                    });
                    $row->width(3)->select('case.case_type')->options(admin_trans('risk-case.options.case_type'))->display(true);
                    $row->width(3)->display('case.auditor');
                    $row->width(3)->select('merchant.source')->options(admin_trans('kyc.options.source'));
                    $row->width(3)->display('case.created_at');
                    $row->width(3)->display('case.completion_at');
                });
            });

            // 网址信息
            $form->tab(admin_trans_label('web'), function (Form $form) {
                $form->row(function (Row $row) {
                    $row->hidden('url_status');
                    $row->width(3)->text('url_name');
                    $row->width(3)->text('main_url_name');
                    $row->width(3)->select('website_mode')->options(MerchantUrlModel::$merchantWebsiteModeMap)->when(1, function (Form $form) {
                        $form->text('saas_url_name')->help('https://xxx.myshoplaza.com');
                    });
                    $row->width(3)->text('remarks');
                });

                $form->fieldset(admin_trans_label('g2'), function (Form $form) {
                    $form->hidden('urlCaseInfo.risk_case_id')->value(request()->input('risk_case_id'));
                    $form->multipleFile('urlCaseInfo.channel_report')->autoUpload()->downloadable()->required()->width(12,0);
                });

                $form->fieldset(admin_trans_label('location'), function (Form $form) {
                    $form->radio('urlCaseInfo.merchant_location_assessment')->options(admin_trans('url.options.check'))->required()->width(12,0);
                    $form->multipleFile('urlCaseInfo.supplementary_annex')->autoUpload()->downloadable()->required()->width(12,0);
                });

                $form->fieldset(admin_trans_label('factors'), function (Form $form) {
                    $form->row(function (Row $row) {
                        $row->hidden('factors.risk_case_id')->value(request()->input('risk_case_id'));
                        $row->width(6)->radio('factors.cryptocurrency')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.dropshipper')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.pre_order')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.startup')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.cannabidiol')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.multi_level_marketing')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.subscriptions_over_one_year')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.buy_now_pay_later')->options(admin_trans('url.options.check'))->required();
                        $row->width(6)->radio('factors.third_party_agents')->options(admin_trans('url.options.check'))->required();
                    });
                });

                $form->fieldset(admin_trans_label('disclosures'), function (Form $form) {
                    $form->row(function (Row $row) {
                        $row->hidden('disclosures.risk_case_id')->value(request()->input('risk_case_id'));
                        $row->hidden('d_mcc_id');
                        $row->width(6)->select('disclosures.mcc')
                            ->options(function () {
                                return RiskMcc::query()
                                              ->where('overall_risk_rating', '!=', 'Prohibited')
                                              ->pluck('mcc', 'id');
                            });
                        $row->width(6)->radio('disclosures.legal_entity_name')->options(admin_trans('url.options.disclosure'))->required();
                        $row->width(6)->radio('disclosures.governing_jurisdiction')->options(admin_trans('url.options.disclosure'))->required();
                        $row->width(6)->radio('disclosures.merchant_company_address')->options(admin_trans('url.options.disclosure'))->required();
                        $row->width(6)->radio('disclosures.terms_and_conditions')->options(admin_trans('url.options.disclosure'))->required();
                        $row->width(6)->radio('disclosures.privacy_policy')->options(admin_trans('url.options.disclosure'))->required();
                        $row->width(6)->radio('disclosures.refund_return_policy')->options(admin_trans('url.options.disclosure'))->required();
                        $row->width(6)->radio('disclosures.customer_service_contact')->options(admin_trans('url.options.disclosure'))->required();
                    });
                });
            });

            // 审核信息
            $form->tab(admin_trans_label('audit_information'), function (Form $form) {
                $form->column(6, function (Form $form) {
                    $form->hidden('pid_status');
                    $form->select('urlCaseInfo.pid_status')->options(admin_trans('kyc.options.audit'))->required();
                });

                $form->column(6, function (Form $form) {
                    $form->textarea('urlCaseInfo.audit_remark')->width(9, 3)->required();
                    $form->textarea('urlCaseInfo.status_remarks')->width(9, 3)->required();
                    $form->multipleFile('urlCaseInfo.audit_file')->width(9, 3)->autoUpload()->downloadable();
                });
            });

            if (request()->input('show')) {
                $form->disableHeader();
                $form->disableFooter();
                Admin::js('/js/show.js');
                Admin::script(<<<'JS'
                        initShowPageBehavior();
                    JS
                );
            }

            $form->saving(function (Form $form) {
                if ($form->isEditing()) {
                    if (!$form->input('urlCaseInfo.pid_status')){
                        return;
                    }

                    $form->pid_status = $form->input('urlCaseInfo.pid_status');
                    $form->d_mcc_id = $form->input('disclosures.mcc');
                    if ($form->pid_status == RiskCase::CASE_AUDIT_APPROVE) {
                        // 案例审核通过不会修改网址状态，最终的网址状态由运营是否进行配置以及启用、停用操作决定
                        $form->url_status = $form->model()->url_status;
                    } else {
                        if ($form->model()->url_status == MerchantUrlModel::STATUS_UNDER_REVIEW) {
                            // 如果网址状态是审核中，说明是首次提交审核，此时的审核结果会影响网址状态
                            $form->url_status = MerchantUrlModel::STATUS_REJECT;
                        } else {
                            // 如果网址状态不是审核中，说明不是首次提交审核，这时候点击审核拒绝，网址和审核状态都保持原样
                            if ($form->input('disclosures.mcc') != $form->model()->d_mcc_id) {
                                return $form->response()->error(admin_trans_label('mcc_check_messages'));
                            }

                            $form->url_status = $form->model()->url_status;
                            $form->pid_status = $form->model()->pid_status;
                        }
                    }

                    // 保存 cases 信息
                    $case   = $form->model()->case;
                    $update = [
                        'completion_at' => now(),
                        'case_status'   => RiskCase::CASE_STATUS_COMPLETE,
                        'audit_result'  => $form->input('urlCaseInfo.pid_status'),
                    ];

                    // 更新 case
                    $case->update($update);
                }
            });
        });
    }
}
