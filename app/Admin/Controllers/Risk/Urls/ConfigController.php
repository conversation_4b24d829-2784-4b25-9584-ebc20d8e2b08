<?php

namespace App\Admin\Controllers\Risk\Urls;

use App\Models\MerchantKyc;
use App\Models\RiskCase;
use App\Models\RiskMcc;
use App\Services\RiskCasesService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Http\Controllers\HasResourceActions;
use Dcat\Admin\Layout\Content;
use App\Jobs\SendNotice;
use App\Models\DirectoryCc;
use App\Models\MerchantUrl as MerchantUrlModel;
use App\Services\UrlService;
use App\Admin\Repositories\DirectoryDictionary;
use function config;
use function dispatch;
use const PHP_EOL;

class ConfigController
{
    use HasResourceActions;

    /**
     * Edit interface.
     *
     * @param mixed $id
     * @param Content $content
     *
     * @return Content
     */
    public function edit($id, Content $content)
    {
        return $content
            ->header(trans('admin.roles'))
            ->description(trans('admin.edit'))
            ->body($this->form()->edit($id));
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        // mcc
        $dictionary = new DirectoryDictionary();
        $mccList    = RiskMcc::query()->where('overall_risk_rating', '!=', 'Prohibited')->pluck('mcc', 'id');
        $brandList  = $dictionary->getTypeList('网址品牌');

        // 卡种
        $directoryCcList = DirectoryCc::all()->where('is_risk_control', DirectoryCc::IS_RISK_CONTROL_OPEN)->pluck('cc_type', 'cc_type');

        app('admin.translator')->setPath('url');
        return Form::make(new MerchantUrlModel(), function (Form $form) use ($mccList, $brandList, $directoryCcList) {
            $formId = $form->getElementId();
            $tips = admin_trans_label('tips');
            $content = admin_trans_label('tips_content');
            Admin::script(
                <<<JS
                var form = $('form#$formId')
                function submit() {
                    Dcat.Form({
                        form: form,
                        success: function () {
                            Dcat.reload();
                            return false
                        }
                    });
                }

                 $('a.layui-layer-btn0').off('click').on('click' ,function(e) {
                     e.preventDefault();

                     var caseMcc = form.find('input[name="case_mcc"]').val();
                     var dMccId = form.find('select[name="d_mcc_id"]').val();
                     if (caseMcc !== dMccId) {
                         Dcat.confirm('$tips', '$content', ()=>{
                             submit();
                         })
                     } else {
                         submit()
                     }
                 })
                JS
            );
            $form->disableSubmitButton();
            $form->hidden('id');
            $form->hidden('url_status');
            $form->hidden('risk_case_id');
            $form->hidden('pid_status');
            $form->hidden('main_url_name');
            $form->text('url_name')->disable();
            $form->text('business_id')->disable();
            $form->select('website_mode')->options(MerchantUrlModel::$merchantWebsiteModeMap)
                 ->when(1, function (Form $form) {
                     $form->text('saas_url_name')->help('https://xxx.myshoplaza.com');
                 });
            $form->select('disclosuresByCase.mcc')->options($mccList)->disable();
            $form->hidden('case_mcc')->value($form->model()->disclosuresByCase->mcc ?? null);
            $form->select('d_mcc_id')->options($mccList);
            $form->select('d_brand_id')->options($brandList);
            $form->checkbox('cc_types')->options($directoryCcList)
                 ->saving(function ($value) {
                     return implode(',', $value);
                 });
        })->saving(function (Form $form) {
            if ($form->model()->url_status == MerchantUrlModel::STATUS_TECHNICAL_REVIEW && !Admin::user()->isAdministrator()) {
                return $form->response()->error('没有权限操作！');
            }

            // 如果原来不为激活状态，则进行配置之后变为激活状态，如果是需要生成案例的情况，则该条件会被忽略
            if ($form->model()->url_status != MerchantUrlModel::STATUS_UP) {
                $form->url_status = MerchantUrlModel::STATUS_UP;
            }

            if ($form->case_mcc != $form->d_mcc_id || $form->model()->d_mcc_id != $form->d_mcc_id) {
                $kyc =MerchantKyc::query()->where('merchant_id', $form->model()->merchant_id)->orderByDesc('id')->first();

                $oldCase = RiskCase::query()->where('id', $form->model()->risk_case_id)->first();
                if ($oldCase && $oldCase->case_status != RiskCase::CASE_STATUS_COMPLETE) {
                    return $form->response()->error('商户案例正在审核中，暂时不支持修改MCC Code！');
                }

                $service = new RiskCasesService();
                $case = $service->addCases([
                                       'merchant_id'   => $form->model()->merchant_id,
                                       'merchant_name' => $form->model()->merchant_name,
                                       'case_type'     => RiskCase::CASE_TYPE_CSC,
                                       'country'       => $kyc->type ?? -1,
                                   ], $form->model()->business_id);

                // 如果为非审核中状态，则网址状态和审核状态保持原状态不变
                $form->url_status = $form->model()->url_status;
                $form->pid_status = $form->model()->pid_status;

                // 产生案例时，如果此时的网址是审核中的状态，说明当前仍属于首次提交网址的审核流程中
                if ($form->model()->url_status == MerchantUrlModel::STATUS_UNDER_REVIEW) {
                    $form->pid_status = RiskCase::CASE_AUDIT_UNDER;
                }

                // 卡组合规审核通过之后，才允许实际的变更MCC Code
                $form->d_mcc_id = $form->model()->d_mcc_id;
                $form->risk_case_id = $case->id;
            }

            $form->deleteInput('case_mcc');
            $form->deleteInput('disclosuresByCase');

            if ($form->website_mode == MerchantUrlModel::WEBSITE_MODE_SHOPLAZZA) {
                $saas_url_name = $form->saas_url_name ?? '';
                if (empty($saas_url_name)) {
                    return $form->response()->error('SaaS网址名称不能为空！');
                }

                // 判断SaaS网址是否存在
                $mainUrlName = UrlService::_getMainUrlName($saas_url_name);
                if (MerchantUrlModel::where('id', '<>', $form->id)->where('url_status', '<>', MerchantUrlModel::STATUS_DELETE)->where(['main_url_name' => $mainUrlName, 'business_id' => $form->model()->business_id])->exists()) {
                    return $form->response()->error('SaaS网址名称已存在！');
                }

                // 建站方式是店匠并且是待审核状态，需要再审核
                if ($form->model()->url_status == MerchantUrlModel::STATUS_CHECK) {
                    $form->url_status = MerchantUrlModel::STATUS_TECHNICAL_REVIEW;
                }
            } else {
                // 建站方式不是店匠，把SaaS网址清空并更新主域名
                $form->saas_url_name = '';
                $mainUrlName         = UrlService::_getMainUrlName($form->model()->url_name);
            }

            $form->main_url_name = $mainUrlName;
        })->saved(function (Form $form, $result) {
            if ($result && $form->model()->url_status == MerchantUrlModel::STATUS_TECHNICAL_REVIEW) {
                // 需要技术审核的SaaS网址发送通知工单
                $data = [
                    'level'             => 1,
                    'contents'          => '[SaaS网址复核]' . PHP_EOL .
                                           'MID:' . $form->model()->merchant_id . PHP_EOL .
                                           '商户:' . $form->model()->merchant_name . PHP_EOL .
                                           'SaaS网址:' . $form->model()->saas_url_name . PHP_EOL .
                                           '来源:' . config('app.url'),
                    'notice_user_roles' => 'Shoplaza Reminder',
                    'type'              => 3,
                    'status'            => 2,
                ];
                dispatch(new SendNotice($data, 5, 'workNotice'));
            }
        });
    }
}
