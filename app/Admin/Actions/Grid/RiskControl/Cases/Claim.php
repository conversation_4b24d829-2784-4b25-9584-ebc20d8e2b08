<?php

namespace App\Admin\Actions\Grid\RiskControl\Cases;

use App\Models\MerchantUrl;
use App\Models\RiskCase;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class Claim extends RowAction
{
    /**
     * 标题
     *
     * @return string
     */
    public function title(): string
    {
        return '<button type="button" class="btn btn-outline-primary">' . admin_trans('risk-case.fields.claim') . '</button>';
    }

    /**
     * 设置确认弹窗信息，如果返回空值，则不会弹出弹窗
     *
     * 允许返回字符串或数组类型
     *
     * @return array
     */
    public function confirm(): array
    {
        return [
            // 确认弹窗 title
            admin_trans_field('claim'),
            // 确认弹窗 content
            admin_trans_field('content') . $this->row->case_number . "?",
        ];
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request): Response
    {
        // 获取当前行ID
        $id   = $this->getKey();
        $user = Admin::user();
        if (!Admin::user()->isRole('Risk Control Operation')) {
            return $this->response()->error('您不是Risk Control Operation，无法领取案例');
        }

        $case = RiskCase::query()->where('user_id', 0)->find($id);

        $case->user_id       = $user->id;
        $case->auditor       = $user->username;
        $case->case_status   = RiskCase::CASE_STATUS_PENDING;
        $case->completion_at = now();

        if ($case->case_type == RiskCase::CASE_TYPE_CSC) {
            DB::beginTransaction();

            try {
                $url = MerchantUrl::query()->where('risk_case_id', $case->id)->first();
                if (!$url->exists()) {
                    return $this->response()->error("领取失败：[{$request->get('case')}]，不存在对应的网址信息");
                }

                if ($url->url_status == MerchantUrl::STATUS_CHECK) {
                    $url->url_status = MerchantUrl::STATUS_UNDER_REVIEW;
                    $url->save();
                }

                $case->save();

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                logger()->channel('Intercept')->warning("领取案例失败：[{$request->get('case')}]，失败原因：" . $e->getMessage());
                return $this->response()->error("领取失败：[{$request->get('case')}]，请稍后再试");
            }
        } else {
            $case->save();
        }

        // 返回响应结果并刷新页面
        return $this->response()->success("领取成功: [{$request->get('case')}]")->refresh();
    }

    /**
     * 设置要POST到接口的数据
     *
     * @return array
     */
    public function parameters(): array
    {
        return [
            'case' => $this->row->case_number,
        ];
    }
}
