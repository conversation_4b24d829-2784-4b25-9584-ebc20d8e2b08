<?php

namespace App\Admin\Actions\Grid\RiskControl\Cases;

use App\Models\MerchantUrl;
use App\Models\RiskCase;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Models\Administrator;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;
use Illuminate\Support\Facades\DB;

class AssignForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;
        if (!$id) {
            return $this->response()->error(admin_trans('risk-case.fields.error.case_not_exist'));
        }

        $case = RiskCase::query()->find($id);
        if (!$case) {
            return $this->response()->error(admin_trans('risk-case.fields.error.case_not_exist'));
        }

        $user = Administrator::query()->find($input['user_id']);

        if ($case->user_id == $user->id) {
            return $this->response()->error(admin_trans('risk-case.fields.error.case_dump'));
        }

        $case->user_id       = $user->id;
        $case->auditor       = $user->username;
        $case->case_status   = RiskCase::CASE_STATUS_PENDING;
        $case->completion_at = now();

        if ($case->case_type == RiskCase::CASE_TYPE_CSC) {
            DB::beginTransaction();

            try {
                $url = MerchantUrl::query()->where('risk_case_id', $case->id)->first();
                if (!$url->exists()) {
                    return $this->response()->error(admin_trans('risk-case.fields.error.url_no_exist'));
                }

                if ($url->url_status == MerchantUrl::STATUS_CHECK) {
                    $url->url_status = MerchantUrl::STATUS_UNDER_REVIEW;
                    $url->save();
                }

                $case->save();

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                logger()->channel('Intercept')->warning("领取案例失败：[$case->case_number]，失败原因：" . $e->getMessage());
                return $this->response()->error(admin_trans('risk-case.fields.error.assign_fail'));
            }
        } else {
            $case->save();
        }

        return $this->response()->success(admin_trans('risk-case.fields.error.submit_success'))->refresh();
    }

    public function form()
    {
        $user = Administrator::whereHas('roles', function ($query) {
            $query->where('slug', RiskCase::getRole());
        })
                             ->pluck('name', 'id')
                             ->toArray();
        $this->select('user_id', admin_trans_field('auditor'))->width(8, 3)->options($user)->required();
    }
}
