<?php

namespace App\Admin\Actions\Grid\RiskControl\Merchant;

use App\Models\Merchant;
use App\Models\MerchantKyc;
use App\Models\MerchantUrl;
use App\Models\RiskCase;
use App\Models\RiskSummary;
use App\Services\RiskCasesService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CaseSummary extends RowAction
{
    /**
     * 标题
     *
     * @return string
     */
    public function title(): string
    {
        return '<button type="button" class="btn btn-outline-info">' . admin_trans_field('risk_summary') . '</button>';
    }

    /**
     * 设置确认弹窗信息，如果返回空值，则不会弹出弹窗
     *
     * 允许返回字符串或数组类型
     *
     * @return array|string|void
     */
    public function confirm()
    {
        return [
            // 确认弹窗 title
            admin_trans_field('help.add'),
            // 确认弹窗 content
            $this->row->merchant_name,
        ];
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request): Response
    {
        // 设置翻译文件路径
        app('admin.translator')->setPath('merchant');

        $merchantId   = $request->get('merchant_id');
        $merchantName = $request->get('merchant_name');

        // 获取该商户数据
        $merchant = Merchant::query()->with(['merchantKyc' => function ($query) {
            $query->where('audit_result', MerchantKyc::STATUS_UP);
        }, 'merchantKyc.case'])->orderBy('created_at', 'desc')->find($merchantId);

        if (!$merchant) {
            return $this->response()->error('商户不存在');
        }

        if (!$merchant->merchantKyc) {
            return $this->response()->error(admin_trans_field('help.kyc_missing'));
        }

        if (!$merchant->merchantKyc->case) {
            return $this->response()->error(admin_trans_field('help.case_missing'));
        }

        // 获取商户网址信息
        $merchantUrl = MerchantUrl::with(['mcc'])
                                  ->select('d_mcc_id', 'url_name', 'business_id')
                                  ->where('merchant_id', $merchantId)
                                  ->where('url_status', MerchantUrl::STATUS_UP)
                                  ->get();
        // 获取网址信息
        $urls = [];
        $bids = [];
        foreach ($merchantUrl as $url) {
            $mcc = $url->mcc;
            if (!$mcc) {
                continue;
            }

            $urls[] = [
                'mcc'         => $url->d_mcc_id,
                'url'         => $url->url_name,
                'description' => $mcc->description
            ];
            $bids[] = $url->business_id;
        }

        // 获取商户KYC案例数据
        $merchantKyc = $merchant->merchantKyc;
        $kycCase     = $merchantKyc->case;

        // 获取所有卡组合规案例数据（不限制审核状态，获取完整历史）
        $schemeComplianceCases = MerchantUrl::with(['case', 'urlCaseInfoByCase'])
                                            ->where('merchant_id', $merchantId)
                                            ->orderBy('created_at', 'desc')
                                            ->get();

        // 构建批准总结数据结构
        $approvalSummary = [
            // 1. Summary
            [
                'team'                => RiskSummary::TEAM_COVER,
                'final_decision'      => '',
                'final_decision_date' => '',
                'approval_conditions' => '',
                'approver_name'       => '',
            ],
            // 2. CDD
            [
                'team'                => RiskSummary::TEAM_CDD,
                'final_decision'      => $kycCase->audit_result,
                'final_decision_date' => $kycCase->completion_at,
                'approval_conditions' => $merchantKyc->audit_remark ?? '',
                'approver_name'       => $kycCase->user_id ?? 0,
                'is_readonly'         => true, // 从KYC数据获取，只读
            ],
            // 3. MLRO
            [
                'team'                => RiskSummary::TEAM_MLRO,
                'final_decision'      => '',
                'final_decision_date' => '',
                'approval_conditions' => '',
                'approver_name'       => '',
            ],
        ];

        $urlCase = [];
        foreach ($schemeComplianceCases as $sc) {
            $urlCase[] = [
                'final_decision'      => $sc['urlCaseInfoByCase']['pid_status'] ??  '',
                'final_decision_date' => $sc['case']['completion_at'] ?? '',
                'approval_conditions' => $sc['urlCaseInfoByCase']['audit_remark'] ?? '',
                'approver_name'       => $sc['case']['user_id'] ?? 0,
                'url'                 => $sc['url_name'],
                'is_readonly'         => true, // 从案例数据获取，只读
            ];
        }

        try {
            DB::transaction(function () use ($merchantId, $merchantName, $merchant, $urls, $approvalSummary, $urlCase, $bids) {
                $caseService = new RiskCasesService();
                $caseData    = [
                    'merchant_id'   => $merchantId,
                    'merchant_name' => $merchantName,
                    'case_type'     => RiskCase::CASE_TYPE_SUMMARY,
                    'country'       => $merchant->merchantKyc->type ?? -1
                ];
                // 创建 case
                $case = $caseService->addCases($caseData, $bids);

                $caseSummary                        = new RiskSummary();
                $caseSummary->merchant_id           = $merchantId;
                $caseSummary->merchant_name         = $merchantName;
                $caseSummary->type                  = $merchant->merchantKyc->type;
                $caseSummary->export_country        = $merchant->merchantKyc->export_country;
                $caseSummary->year_of_incorporation = date('Y', strtotime($merchant->merchantKyc->found_date));
                $caseSummary->urls                  = $urls;
                $caseSummary->urls_case             = $urlCase;
                $caseSummary->approval_summary      = $approvalSummary;
                $caseSummary->riskCase()->associate($case);
                $caseSummary->save();
            });
        } catch (\Throwable $e) {
            return $this->response()->error($e->getMessage());
        }


        // 返回响应结果并刷新页面
        return $this->response()->success(admin_trans_field('help.success') . ": [$merchantName]")->refresh();
    }

    /**
     * 设置要POST到接口的数据
     *
     * @return array
     */
    public function parameters(): array
    {
        return [
            'merchant_id'   => $this->row->merchant_id,
            'merchant_name' => $this->row->merchant_name,
        ];
    }
}
