<?php

namespace App\Admin\Actions\Grid\RiskControl\CreditAssessment;

use App\Models\MerchantKyc;
use App\Models\MerchantUrl;
use App\Models\RiskCase;
use App\Models\RiskCreditAssessment;
use App\Services\RiskCasesService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;

class Create extends RowAction
{
    /**
     * @return string
     */
	protected $title = '';

    public function __construct()
    {
        parent::__construct();
        $this->title = admin_trans('credit-assessment.labels.create_case');
    }

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request): Response
    {
        $merchantId = $request->get('merchant_id');
        $businessId = $request->get('business_id');
        $urlList = MerchantUrl::query()->with(['mcc'])
            ->where('merchant_id', $merchantId)
            ->where('business_id', $businessId)
            ->get();
        $kyc = MerchantKyc::query()->where('merchant_id', $merchantId)->orderByDesc('id')->first();
        $mccTable = [];
        $ndxDetails = RiskCreditAssessment::NDX_DETAIL_REQUIRED_TEMPLATE_HEADER;
        $mccIds = [];
        foreach ($urlList as $url) {
            $mccTable[] = [
                'id' => $url->d_mcc_id,
                'mcc' => $url->mcc->mcc ?? '',
                'mcc_description' => $url->mcc->description ?? '',
                'credit_risk_rating' => $url->mcc->overall_risk_rating ?? '',
                'delivery_days' => null,
                'percentage_of_apv' => null,
                'merchant_website' => $url->url_name,
            ];
            $ndxDetails[] = [
                'title' => $url->mcc->mcc ?? '',
                'days' => null,
                'rate' => null,
                'amount' => null,
            ];

            if ($url->mcc) {
                $mccIds[] = $url->d_mcc_id;
            }
        }

        $ndxDetails = array_merge($ndxDetails, RiskCreditAssessment::NDX_DETAIL_REQUIRED_TEMPLATE_FOOTER);

        // 对mccId去重
        $mccIds = array_unique($mccIds);

        // 创建对应的风险案列和风险信用评级报告记录
        $caseService = new RiskCasesService();
        $case = $caseService->addCases([
            'merchant_id'   => $merchantId,
            'merchant_name' => $kyc->company_name,
            'case_type'     => RiskCase::CASE_TYPE_CRA,
            'country'       => $kyc->type ?? -1
        ], $businessId);

        $creditAssessment = new RiskCreditAssessment();
        $creditAssessment->risk_case_id = $case->id;
        $creditAssessment->mcc_table = json_encode($mccTable);
        $creditAssessment->ndx_details = json_encode($ndxDetails);
        $creditAssessment->company_name = $kyc->company_name;
        $creditAssessment->merchant_id = $merchantId;
        $creditAssessment->mcc_id = implode(',', $mccIds);
        $creditAssessment->reserve_currency = 'USD';
        $creditAssessment->save();

        return $this->response()
            ->success(admin_trans('credit-assessment.labels.create_success'));
    }

    /**
	 * @return string
     */
	public function confirm(): string
    {
		 return admin_trans('credit-assessment.labels.create_confirm');
	}

    protected function parameters(): array
    {
        return [
            'merchant_id' => $this->row->merchant_id,
            'business_id' => $this->row->business_id,
        ];
    }
}
