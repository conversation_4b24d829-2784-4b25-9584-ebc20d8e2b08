<?php

use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Navbar;

/**
 * Dcat-admin - admin builder based on Lara<PERSON>.
 * <AUTHOR> <https://github.com/jqhph>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 *
 * extend custom field:
 * Dcat\Admin\Form::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Column::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Filter::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */
app('view')->prependNamespace('admin', resource_path('views/vendor/admin'));

$date   = date('Y');
switch (env('APP_NAME', 'Laravel')) {
    case 'Embracy':
        $company = 'Embracy';
        break;

    case 'Hpaymerchants':
        $company = 'Hpaymerchants';
        break;

    case 'PunctualPay':
        $company = 'Punctual Pay';
        break;

    default:
        $company = 'Peachy Pay';
        break;
}

$footer = <<<html
<p class="clearfix blue-grey lighten-2 mb-0 text-center"><span class="text-center d-block d-md-inline-block mt-25">Copyright © {$date} {$company}. All Rights Reserved.</span></p>
html;

Admin::script(
    <<<JS
        $(".main-footer").html('{$footer}');
        $('#grid-table').parent().attr('id', 'table_id');

        let tableCont = document.querySelectorAll('table.grid-table  thead');
        
        function cloneNodeWithStyles(element) {
            const cloned = element.cloneNode(true);  
            const originalStyles = window.getComputedStyle(element);  

            // 复制所有计算样式（过滤浏览器私有属性）
            Array.from(originalStyles).forEach(prop  => {
                if (!prop.startsWith('-'))  {
                    cloned.style[prop]  = originalStyles.getPropertyValue(prop);  
                }
            });

            // 特殊处理子元素（TH/TD）
            cloned.querySelectorAll('th,  td').forEach((child, index) => {
                const originalChild = element.children[0].children[index];  
                const childStyles = window.getComputedStyle(originalChild);  
                Array.from(childStyles).forEach(prop  => {
                    if (!prop.startsWith('-'))  {
                        child.style[prop]  = childStyles.getPropertyValue(prop);  
                    }
                });
            });

            return cloned;
        }
        // 更新固定表头位置和显隐 
        function updateFixedHeaders(initTop = 0) {
            requestAnimationFrame(() => {
                tableCont.forEach(table => {
                    const rect = table.getBoundingClientRect(); 
                    const tableParent = table.closest('table').parentElement;  // 获取表格的父容器 
                    let fixed = tableParent.querySelector('.table-fixed-header'); 

                    if (!fixed) {
                        const item = cloneNodeWithStyles(table);
                        item.style.display  = 'none';
                        item.style.position  = 'fixed';
                        item.style.top  = initTop  + 'px';
                        item.style.left  = rect.left  + 'px'; // 初始 left 位置 
                        item.style.width  = table.offsetWidth  + 'px';
                        item.style.zIndex  = 999;
                        item.classList.add('table-fixed-header'); 
                        tableParent.appendChild(item); 
                        fixed = item;

                        // 监听表格父容器的水平滚动 
                        tableParent.addEventListener('scroll',  function() {
                            const scrollLeft = tableParent.scrollLeft; 
                            fixed.style.left  = (rect.left  - scrollLeft) + 'px'; // 调整 left 位置 
                        });
                    }
                    let scrollTop = document.getElementById('table_id').scrollTop;
                    if (rect.top  + scrollTop < 33) {
                        fixed.style.display  = 'block';
                    } else {
                        fixed.style.display  = 'none';
                    }

                    // 确保水平滚动时固定表头的位置同步 
                    const scrollLeft = tableParent.scrollLeft; 
                    fixed.style.left = (rect.left  - scrollLeft) + 'px';
                });
            });
        }
         // 监听iframe变动
        const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateFixedHeaders(0)
                }
            });
        });

        let contentHeader = document.querySelector('#pjax-container > div.content-header')
        if (contentHeader) {
            observer.observe(contentHeader);
        }

        // 初始化监听 
        window.addEventListener('scroll',  () => updateFixedHeaders(0));
        window.addEventListener('resize',  () => updateFixedHeaders(0));
    JS
);

//固定表格表头样式
Admin::style(
    <<<STYLE
        .data-table {
            border-collapse: collapse;
            width: 100%;
        }
        .data-table thead th {
            position: sticky;
            top: 0;
            background-color: #ECECF1;
            z-index: 10;
        }
        .pagination .page-item.page-item-input__wrap {
            border: none !important;
            display: flex;
            align-items: center;
            line-height: 1.3;
            background-color: transparent !important;
            color: #ADB6C2;
            margin-left: 12px;
        }

        .pagination .page-item .page-item-input__label {
            margin-right: 8px;
        }

        .pagination .page-item .page-item-input__input {
            min-width: 30px;
            border: none;
            background-color: transparent !important;
            color: #606266;
            text-align: center;
            border: 1px solid #ADB6C2;
            border-radius: 6px;
            margin-right: 24px;
            padding-left: 12px;
            padding-right: 12px;
        }

    STYLE
);

Admin::navbar(function (Navbar $navbar) {
    if (config('open_im.open_im')) {
        $navbar->right(view('navbar.openIm', ['system' => 'admin']));
    }
});
