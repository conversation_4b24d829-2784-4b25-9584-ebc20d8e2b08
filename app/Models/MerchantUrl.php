<?php

namespace App\Models;

use App\Scopes\Bid;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class MerchantUrl extends Model
{
    use HasDateTimeFormatter;

    protected $guarded = [];

    protected $fillable = [
        'merchant_id', 'business_id', 'merchant_name', 'url_name', 'main_url_name', 'd_mcc_id', 'd_brand_id', 'cc_types', 'url_status', 'pid_status', 'remarks'
    ];

    protected $casts = [
        'channel_report' => 'array',
    ];

    // 网址状态
    const STATUS_DOWN             = 0;
    const STATUS_UP               = 1;
    const STATUS_CHECK            = 2;
    const STATUS_TECHNICAL_REVIEW = 5;
    const STATUS_DELETE           = 7;
    const STATUS_REJECT           = 8;
    const STATUS_UNDER_REVIEW     = 9;

    public static $merchantStatusMap = [
        self::STATUS_DOWN             => '关停',
        self::STATUS_UP               => '激活',
        self::STATUS_CHECK            => '待审核',
        self::STATUS_TECHNICAL_REVIEW => '待技术审核',
        self::STATUS_DELETE           => '删除',
        self::STATUS_REJECT           => '驳回',
        self::STATUS_UNDER_REVIEW     => '审核中',
    ];

    // 建站方式
    const WEBSITE_MODE_STAND_ALONE = 0;
    const WEBSITE_MODE_SHOPLAZZA   = 1;
    const WEBSITE_MODE_SHOPYY      = 2;

    public static $merchantWebsiteModeMap = [
        self::WEBSITE_MODE_STAND_ALONE => '自建站',
        self::WEBSITE_MODE_SHOPLAZZA   => '店匠',
        self::WEBSITE_MODE_SHOPYY      => 'Shopyy',
    ];

    public function business()
    {
        return $this->belongsTo(MerchantBusiness::class, 'business_id', 'business_id');
    }

    public function merchant()
    {
        return $this->belongsTo(Merchant::class, 'merchant_id', 'merchant_id');
    }

    public function brand()
    {
        return $this->belongsTo(DirectoryDictionary::class, 'd_brand_id', 'id');
    }

    public function mcc()
    {
        return $this->belongsTo(RiskMcc::class, 'd_mcc_id', 'id');
    }

    public function factors()
    {
        return $this->hasOne(MerchantHighRiskFactor::class, 'merchant_url_id', 'id');
    }

    public function disclosures()
    {
        return $this->hasOne(MerchantWebsiteDisclosure::class, 'merchant_url_id', 'id');
    }

    public function disclosuresByCase()
    {
        return $this->hasOne(MerchantWebsiteDisclosure::class, 'risk_case_id', 'risk_case_id');
    }

    public function urlCaseInfo()
    {
        return $this->hasOne(MerchantUrlCaseInfo::class, 'merchant_url_id', 'id');
    }

    public function urlCaseInfoByCase()
    {
        return $this->hasOne(MerchantUrlCaseInfo::class, 'risk_case_id', 'risk_case_id');
    }

    public function case()
    {
        return $this->belongsTo(RiskCase::class, 'risk_case_id', 'id');
    }

    /**
     * 启动添加scope
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new Bid());
    }
}
